package com.tfkcolin.meena.data.models.firebase

import com.google.firebase.firestore.DocumentId
import com.google.firebase.firestore.ServerTimestamp
import java.util.*

/**
 * Firebase-compatible data models for Firestore storage.
 * These models are optimized for Firestore's document structure and real-time capabilities.
 */

/**
 * Firebase user profile model for Firestore storage.
 */
data class FirebaseUserProfile(
    @DocumentId
    val userId: String = "",
    val userHandle: String = "",
    val email: String? = null,
    val phoneNumber: String? = null,
    val displayName: String = "",
    val bio: String = "",
    val avatarUrl: String = "",
    val subscriptionTier: String = "free", // "free", "gold"
    val verificationStatus: String = "none", // "none", "pending", "verified"
    val isActive: Boolean = true,
    @ServerTimestamp
    val createdAt: Long = 0,
    @ServerTimestamp
    val lastActive: Long = 0,
    val followerCount: Int = 0,
    val followingCount: Int = 0
)

/**
 * Firebase contact model for Firestore storage.
 */
data class FirebaseContact(
    @DocumentId
    val id: String = "",
    val userId: String = "",
    val contactId: String = "",
    val displayName: String? = null,
    val relationship: String = "pending", // "friend", "blocked", "pending"
    val notes: String? = null,
    val isFavorite: Boolean = false,
    @ServerTimestamp
    val createdAt: Long = 0,
    @ServerTimestamp
    val updatedAt: Long = 0,
    @ServerTimestamp
    val lastInteractionAt: Long? = null
)

/**
 * Firebase chat model for Firestore storage.
 */
data class FirebaseChat(
    @DocumentId
    val id: String = "",
    val conversationType: String = "one_to_one", // "one_to_one", "group", "channel"
    val privacyType: String? = null, // "public", "private", "secret"
    val participantIds: List<String> = emptyList(),
    val name: String? = null, // Group/channel name
    val description: String? = null,
    val avatarUrl: String? = null,
    val adminIds: List<String> = emptyList(),
    val createdBy: String? = null,
    val lastMessage: String? = null,
    @ServerTimestamp
    val lastMessageTimestamp: Long = 0,
    val unreadCounts: Map<String, Int> = emptyMap(), // userId -> unread count
    val isArchived: Boolean = false,
    val isMuted: Boolean = false,
    val isPinned: Boolean = false,
    val mutedUntil: Long? = null,
    val settings: Map<String, Any>? = null,
    val maxMembers: Int = 20,
    @ServerTimestamp
    val createdAt: Long = 0,
    @ServerTimestamp
    val updatedAt: Long = 0
)

/**
 * Firebase message model for Firestore storage.
 */
data class FirebaseMessage(
    @DocumentId
    val id: String = "",
    val chatId: String = "",
    val senderId: String = "",
    val recipientId: String = "",
    val content: String = "",
    val contentType: String = "text", // "text", "image", "video", "audio", "document", "location"
    val mediaUrl: String? = null,
    val hasAttachments: Boolean = false,
    @ServerTimestamp
    val timestamp: Long = 0,
    val status: String = "sent", // "sending", "sent", "delivered", "read", "failed"
    val isEdited: Boolean = false,
    val deletedFor: List<String> = emptyList(), // List of user IDs who deleted this message
    val replyToMessageId: String? = null,
    val forwardFromMessageId: String? = null,
    val forwardFromChatId: String? = null,
    val forwardFromUserId: String? = null,
    val reactions: Map<String, List<String>> = emptyMap(), // emoji -> list of user IDs
    val readBy: Map<String, Long> = emptyMap(), // userId -> timestamp when read
    val deliveredTo: Map<String, Long> = emptyMap(), // userId -> timestamp when delivered
    @ServerTimestamp
    val createdAt: Long = 0,
    @ServerTimestamp
    val updatedAt: Long = 0
)

/**
 * Firebase media attachment model for Firestore storage.
 */
data class FirebaseMediaAttachment(
    @DocumentId
    val id: String = "",
    val messageId: String = "",
    val type: String = "", // "image", "video", "audio", "document", "location"
    val url: String = "",
    val thumbnailUrl: String? = null,
    val name: String? = null,
    val size: Long? = null,
    val duration: Long? = null, // For audio/video
    val width: Int? = null, // For images/videos
    val height: Int? = null, // For images/videos
    val latitude: Double? = null, // For location
    val longitude: Double? = null, // For location
    @ServerTimestamp
    val createdAt: Long = 0
)

/**
 * Firebase contact group model for Firestore storage.
 */
data class FirebaseContactGroup(
    @DocumentId
    val id: String = "",
    val userId: String = "",
    val name: String = "",
    val description: String? = null,
    val contactIds: List<String> = emptyList(),
    val color: String? = null,
    val icon: String? = null,
    @ServerTimestamp
    val createdAt: Long = 0,
    @ServerTimestamp
    val updatedAt: Long = 0
)

/**
 * Firebase typing indicator model for real-time updates.
 */
data class FirebaseTypingIndicator(
    val chatId: String = "",
    val userId: String = "",
    val isTyping: Boolean = false,
    @ServerTimestamp
    val timestamp: Long = 0
)

/**
 * Firebase presence model for user online status.
 */
data class FirebasePresence(
    @DocumentId
    val userId: String = "",
    val isOnline: Boolean = false,
    @ServerTimestamp
    val lastSeen: Long = 0,
    val status: String = "offline" // "online", "away", "busy", "offline"
)

/**
 * Firebase notification token model for push notifications.
 */
data class FirebaseNotificationToken(
    @DocumentId
    val id: String = "",
    val userId: String = "",
    val token: String = "",
    val deviceType: String = "android", // "android", "ios", "web"
    val deviceId: String = "",
    @ServerTimestamp
    val createdAt: Long = 0,
    @ServerTimestamp
    val updatedAt: Long = 0
)

/**
 * Firebase story model for Firestore storage.
 */
data class FirebaseStory(
    @DocumentId
    val id: String = "",
    val userId: String = "",
    val mediaUrl: String = "",
    val mediaType: String = "image", // "image", "video"
    val thumbnailUrl: String? = null,
    val caption: String? = null,
    val privacyType: String = "contacts", // "public", "contacts", "close_friends", "custom"
    val viewCount: Int = 0,
    @ServerTimestamp
    val createdAt: Long = 0,
    val expiresAt: Long = 0,
    val isExpired: Boolean = false,
    val isHighlighted: Boolean = false,
    val highlightId: String? = null,
    val backgroundColor: String? = null,
    val textColor: String? = null,
    val allowedViewerIds: List<String> = emptyList(),
    val viewerIds: List<String> = emptyList()
) {
    /**
     * Convert Firebase story model to domain Story model.
     */
    fun toStory(): com.tfkcolin.meena.data.models.Story = com.tfkcolin.meena.data.models.Story(
        id = id,
        userId = userId,
        mediaUrl = mediaUrl,
        mediaType = mediaType,
        thumbnailUrl = thumbnailUrl,
        caption = caption,
        privacyType = privacyType,
        viewCount = viewCount,
        createdAt = createdAt,
        expiresAt = expiresAt,
        isExpired = isExpired,
        isHighlighted = isHighlighted,
        highlightId = highlightId,
        backgroundColor = backgroundColor,
        textColor = textColor,
        allowedViewerIds = allowedViewerIds.joinToString(","),
        viewerIds = viewerIds.joinToString(",")
    )
}

/**
 * Firebase story view model for Firestore storage.
 */
data class FirebaseStoryView(
    @DocumentId
    val id: String = "",
    val storyId: String = "",
    val viewerId: String = "",
    @ServerTimestamp
    val viewedAt: Long = 0,
    val viewDuration: Long = 0, // Duration in milliseconds
    val reactionType: String? = null // Emoji reaction
) {
    /**
     * Convert Firebase story view model to domain StoryView model.
     */
    fun toStoryView(): com.tfkcolin.meena.data.models.StoryView = com.tfkcolin.meena.data.models.StoryView(
        id = id,
        storyId = storyId,
        viewerId = viewerId,
        viewedAt = viewedAt,
        viewDuration = viewDuration,
        reactionType = reactionType
    )
}

/**
 * Firebase story highlight model for Firestore storage.
 */
data class FirebaseStoryHighlight(
    @DocumentId
    val id: String = "",
    val userId: String = "",
    val title: String = "",
    val coverUrl: String? = null,
    val storyIds: List<String> = emptyList(),
    @ServerTimestamp
    val createdAt: Long = 0,
    @ServerTimestamp
    val updatedAt: Long = 0
) {
    /**
     * Convert Firebase story highlight model to domain StoryHighlight model.
     */
    fun toStoryHighlight(): com.tfkcolin.meena.data.models.StoryHighlight = com.tfkcolin.meena.data.models.StoryHighlight(
        id = id,
        userId = userId,
        title = title,
        coverUrl = coverUrl,
        storyIds = storyIds.joinToString(","),
        createdAt = createdAt,
        updatedAt = updatedAt
    )
}
